name: wallhammer
description: "The convenient Wallpaper app."
publish_to: 'none'
version: 1.0.18+18

environment:
  sdk: '>=3.3.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  firebase_core: ^3.10.1
  firebase_auth: ^5.4.1
  google_sign_in: ^6.2.2
  flutter_signin_button: ^2.0.0
  cloud_firestore: ^5.6.2
  cached_network_image: ^3.4.1
  firebase_storage: ^12.4.1
  google_mobile_ads: ^4.0.0
  in_app_purchase: ^3.2.1
  path_provider: ^2.1.5
  dio: ^5.8.0+1
  permission_handler: ^11.4.0
  saver_gallery: ^4.0.1
  device_info_plus: ^11.3.3
  intl: ^0.20.2
  flutter_launcher_icons: ^0.14.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/img/

  # fonts:
  #   - family: <PERSON><PERSON><PERSON>
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan <PERSON>
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
