import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:wallhammer/model/wallpaper.dart';

class WallpaperNetworkImage extends StatefulWidget {
  final Wallpaper wallpaper;
  final BoxFit fit;
  final double? height;
  final double? errorIconSize;

  const WallpaperNetworkImage({
    required this.wallpaper,
    this.fit = BoxFit.cover,
    this.height,
    this.errorIconSize = 50.0,
    super.key,
  });

  @override
  State<WallpaperNetworkImage> createState() => _WallpaperNetworkImageState();
}

class _WallpaperNetworkImageState extends State<WallpaperNetworkImage> {
  late final Future<String?> imageUrlFuture;

  @override
  void initState() {
    super.initState();
    imageUrlFuture = widget.wallpaper.fetchImageUrl();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: imageUrlFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          print(snapshot.error);
          print(snapshot.stackTrace);
          return SizedBox(
              height: widget.height,
              child: Icon(Icons.error, size: widget.errorIconSize));
        }

        if (snapshot.requireData == null) {
          return const Text('No image found');
        }

        return Hero(
          tag: widget.wallpaper.id,
          child: CachedNetworkImage(
            imageUrl: snapshot.requireData!,
            fit: widget.fit,
            placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
            errorWidget: (context, url, error) => const Icon(Icons.error),
            height: widget.height,
          ),
        );
      },
    );
  }
}
