import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wallhammer/components/icon_text_button.dart';
import 'package:wallhammer/components/navigation_menu.dart';
import 'package:wallhammer/components/wallpaper_overview_item.dart';
import 'package:wallhammer/main.dart';
import 'package:wallhammer/model/app_user.dart';
import 'package:wallhammer/util/authentication_util.dart';
import 'package:wallhammer/util/string_util.dart';

import '../components/category_selector.dart';
import '../model/wallpaper.dart';
import 'user_screen.dart';

class WallpaperListScreen extends StatefulWidget {
  const WallpaperListScreen({super.key});

  @override
  State<WallpaperListScreen> createState() => _WallpaperListScreenState();
}

class _WallpaperListScreenState extends State<WallpaperListScreen> {
  Future<List<Wallpaper>>? wallpaperFuture;
  String? selectedCategory;

  
  final debugBannerAdId = 'ca-app-pub-3940256099942544/6300978111';
  // final debugBannerAdId = 'ca-app-pub-9147916407761104/5967871843';
  final productionBannerAdId = Platform.isAndroid
      ? 'ca-app-pub-9147916407761104/5967871843'
      : const String.fromEnvironment('AD_BANNER_IOS');
  BannerAd? bannerAd;

  bool showAd = false;

  @override
  void initState() {
    super.initState();
    wallpaperFuture = _loadWallpaperDataset();
    loadShowAd();
  }

  Future<void> loadShowAd() async {
    try {
      final appUser = await loadAppUser(FirebaseAuth.instance.currentUser);
      setState(() {
        // Show ads ONLY for Free users
        // Basic, Pro, and Ultra users should NOT see banner ads (they paid for ad removal)
        showAd = appUser == null || appUser.subscriptionTier == AppUserSubscription.Free;

        if (showAd) {
          _loadBannerAd();
        }
      });
    } catch (e) {
      // If Firebase is not available, show ads by default for testing
      if (kDebugMode) {
        print('Firebase error, showing ads by default: $e');
      }
      setState(() {
        showAd = true;
        _loadBannerAd();
      });
    }
  }

  Future<List<Wallpaper>> _loadWallpaperDataset() async {
    final database = FirebaseFirestore.instance;
    final wallpapers = await database.collection('wallpapers')
        .get().then((snapshot) => snapshot.docs
        .map((e) => Wallpaper.fromFirestore(e.id, e.data())).toList());
    return wallpapers;
  }

  void _loadBannerAd() {
    final adUnitId = kDebugMode ? debugBannerAdId : productionBannerAdId;

    bannerAd = BannerAd(
      adUnitId: adUnitId,
      size: AdSize.banner,
      request: const AdRequest(
        keywords: ['wallpaper', 'mobile', 'android'],
        nonPersonalizedAds: false,
      ),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          if (kDebugMode) print('✅ Banner ad loaded successfully with ID: $adUnitId');
          setState(() {});
        },
        onAdFailedToLoad: (ad, error) {
          if (kDebugMode) {
            print('❌ Banner Ad failed to load: $error');
            print('🔍 Ad Unit ID used: $adUnitId');
            print('🔍 Error code: ${error.code}');
            print('🔍 Error message: ${error.message}');
          }
          ad.dispose();
        },
      ),
    )..load();
  }

  @override
  void dispose() {
    bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Wallhammer')),
      drawer: const NavigationMenu(),
      body: Column(
        children: [
          Expanded(
            child: FutureBuilder(
              future: wallpaperFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  print(snapshot.error);
                  print(snapshot.stackTrace);
                  return const Center(child: Text('An error occurred'));
                }

                final dataset = snapshot.requireData;
                final allCategories = dataset
                    .map((e) => e.categories)
                    .expand((i) => i)
                    .toSet();

                final filteredWallpapers = selectedCategory == null
                    ? dataset
                    : dataset.where((wp) => wp.categories.contains(selectedCategory)).toList();

                return Column(
                  children: [
                    const SizedBox(height: 15.0),

                    CategorySelector(
                      categories: allCategories,
                      selectedCategory: selectedCategory,
                      onSelected: (value) => setState(() => selectedCategory = value),
                    ),

                    const SizedBox(height: 20.0),

                    Flexible(
                      child: filteredWallpapers.isEmpty
                          ? const Center(child: Text('No wallpapers found.'))
                          : GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 9 / 16,
                          mainAxisSpacing: 0.0,
                          crossAxisSpacing: 0.0,
                        ),
                        itemCount: filteredWallpapers.length,
                        itemBuilder: (context, index) {
                          final wallpaper = filteredWallpapers[index];
                          return AspectRatio(
                            aspectRatio: 9 / 16,
                            child: WallpaperOverviewItem(
                              wallpaper: wallpaper,
                              dataset: dataset,
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 15.0),
                  ],
                );
              },
            ),
          ),

          if (showAd && bannerAd != null)
            Container(
              height: bannerAd!.size.height.toDouble(),
              width: bannerAd!.size.width.toDouble(),
              alignment: Alignment.center,
              child: AdWidget(ad: bannerAd!),
            ),

          const SizedBox(height: 10.0),
        ],
      ),
    );
  }
}
